'use client';

import { useEffect, useState } from 'react';
import { Plus, Edit, Trash2, Image as ImageIcon, Video, FileText, Phone, Mail, MapPin, Instagram, Twitter, Facebook, Linkedin, Globe, LogOut } from 'lucide-react';
import { useData } from '@/contexts/DataContext';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import { useGetBio } from '@/hooks/use-get-bio';
import { useCreateBio } from '@/hooks/use-create-bio';
import { useUpdateBio } from '@/hooks/use-update-bio';
import { useGetContactInfo } from '@/hooks/use-get-contact-info';
import { useCreateContactInfo } from '@/hooks/use-create-contact-info';
import { useUpdateContactInfo } from '@/hooks/use-update-contact-info';
import { useGetAdminBehindTheScene } from '@/hooks/get-admin-behind-the-scene';
import { useCreateBehindTheScene } from '@/hooks/use-create-behind-the-scene';
import { useGetAdminGalleries } from '@/hooks/use-get-admin-galleries';
import { useCreateGallery } from '@/hooks/use-create-galleries';
import { useUpdateBehindTheScene } from '@/hooks/use-update-behind the scene';
import { useUpdateGallery } from '@/hooks/use-update-galleries';
import { useGetImageSignature } from '@/hooks/use-get-image-signature';
import { useUploadCloudinary } from '@/hooks/use-upload-cloudinary';

export default function AdminPage() {
  return (
    <ProtectedRoute>
      <AdminContent />
    </ProtectedRoute>
  );
}

function AdminContent() {
  const { artworks, videos, bio, isLoading } = useData();
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState<'artworks' | 'videos' | 'bio' | 'contact'>('artworks');

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'artworks', label: 'Artworks', icon: ImageIcon },
    { id: 'videos', label: 'Videos', icon: Video },
    { id: 'bio', label: 'Bio', icon: FileText },
    { id: 'contact', label: 'Contact & Social', icon: Phone },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-start">
          <div>
            <h1 className="mt-10 text-4xl font-bold text-gray-900 mb-2">Admin Panel</h1>
            <p className="text-gray-600">Manage your portfolio content</p>
          </div>
          <div className="mt-10 flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-600">Welcome back,</p>
              <p className="font-medium text-gray-900">{user?.username}</p>
            </div>
            <button
              onClick={logout}
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
            >
              <LogOut size={16} className="mr-2" />
              Logout
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center py-4 px-2 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-orange-500 text-orange-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon size={20} className="mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'artworks' && <ArtworksTab />}
            {activeTab === 'videos' && <VideosTab  />}
            {activeTab === 'bio' && <BioTab  />}
            {activeTab === 'contact' && <ContactTab />}
          </div>
        </div>
      </div>
    </div>
  );
}

function ArtworksTab() {
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isFetching, isLoading, isError } = useGetAdminGalleries();
  const { mutate: createGallery, isPending: isCreating } = useCreateGallery();
  const { mutate: updateGallery, isPending: isUpdating } = useUpdateGallery();
  const { user, setUser } = useAuth();
  const { data: imageSignature } = useGetImageSignature('image');
  const { mutate: uploadImage, isPending: isUploading } = useUploadCloudinary();

  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedArtwork, setSelectedArtwork] = useState<any>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    caption: '',
    display: true,
    dimension: '',
    year: ''
  });

  const handleEdit = (artwork: any) => {
    setSelectedArtwork(artwork);
    setFormData({
      title: artwork.Title,
      caption: artwork.Caption,
      display: artwork.Display,
      dimension: artwork.Dimension || '',
      year: artwork.Year || ''
    });
    setShowEditForm(true);
  };

  const handleDelete = async (artworkId: number) => {
    setSelectedArtwork({ ID: artworkId });
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    try {
      console.log('Deleting artwork:', selectedArtwork.ID);
      setShowDeleteModal(false);
      setSelectedArtwork(null);
    } catch (error) {
      console.error('Error deleting artwork:', error);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (showEditForm) {
        updateGallery({
          data: {
            ImageUrl: selectedArtwork.ImageUrl,
            Display: formData.display,
            Title: formData.title || undefined,
            Caption: formData.caption || undefined,
            Dimension: formData.dimension || undefined,
            Year: formData.year || undefined
          },
          id: selectedArtwork.ID
        });
      } else {
        if (!selectedFile || !imageSignature?.Data) {
          alert('Please select an image file');
          return;
        }

        uploadImage(
          {
            file: selectedFile,
            signature: imageSignature.Data.signature,
            timestamp: imageSignature.Data.timestamp,
            api_key: imageSignature.Data.api_key,
            cloud_name: imageSignature.Data.cloud_name,
            resource_type: imageSignature.Data.resource_type,
            upload_preset: imageSignature.Data.upload_preset,
          },
          {
            onSuccess: (imageUrl) => {
              createGallery({
                ImageUrl: imageUrl,
                Display: formData.display,
                Title: formData.title || undefined,
                Caption: formData.caption || undefined,
                Dimension: formData.dimension || undefined,
                Year: formData.year || undefined
              });
            }
          }
        );
      }

      if (!isUploading && !isCreating && !isUpdating) {
        setShowAddForm(false);
        setShowEditForm(false);
        setFormData({
          title: '',
          caption: '',
          display: true,
          dimension: '',
          year: ''
        });
        setSelectedFile(null);
      }
    } catch (error) {
      console.error('Error saving artwork:', error);
      alert('Failed to save artwork. Please try again.');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  if (isLoading || isFetching || isUploading || isCreating || isUpdating) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            {isUploading ? 'Uploading image...' : 
             isCreating ? 'Creating artwork...' :
             isUpdating ? 'Updating artwork...' :
             'Loading artworks...'}
          </p>
        </div>
      </div>
    );
  }

  if (isError) {
    localStorage.removeItem('auth_user');
    localStorage.removeItem('auth_token');
    setUser(null);
    return null;
  }

  return (
    <div>
    <div className="flex justify-between items-center mb-6">
      <h2 className="text-2xl font-bold text-gray-900">Manage Artworks</h2>
      <button
        onClick={() => setShowAddForm(true)}
        className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center"
      >
        <Plus size={20} className="mr-2" />
        Add Artwork
      </button>
    </div>

    {/* Delete Confirmation Modal */}
    {showDeleteModal && (
      <div className="fixed inset-0 bg-black/80 bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Delete</h3>
          <p className="text-gray-700 mb-6">Are you sure you want to delete this artwork? This action cannot be undone.</p>
          <div className="flex justify-end space-x-4">
            <button
              onClick={() => {
                setShowDeleteModal(false);
                setSelectedArtwork(null);
              }}
              className="px-4 py-2 border border-gray-200 rounded-lg text-gray-700 hover:text-gray-900 cursor-pointer"
            >
              Cancel
            </button>
            <button
              onClick={confirmDelete}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 cursor-pointer"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    )}

    {(showAddForm || showEditForm) && (
      <div className="bg-gray-50 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4">
          {showEditForm ? 'Edit Artwork' : 'Add New Artwork'}
        </h3>
        <form onSubmit={handleFormSubmit} className="grid grid-cols-1 gap-4">
          <div className="flex flex-col space-y-2">
            <label className="text-sm font-medium text-gray-700">
              {showEditForm ? 'Change Image' : 'Upload Image'}
            </label>
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              required={!showEditForm}
            />
          </div>
          <input
            type="text"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            placeholder="Title"
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
          <textarea
            name="caption"
            value={formData.caption}
            onChange={handleInputChange}
            placeholder="Caption"
            rows={3}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
          <input
            type="text"
            name="dimension"
            value={formData.dimension}
            onChange={handleInputChange}
            placeholder="Dimensions"
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
          <input
            type="text"
            name="year"
            value={formData.year}
            onChange={handleInputChange}
            placeholder="Year"
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                name="display"
                checked={formData.display}
                onChange={handleInputChange}
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="text-sm text-gray-700">Display on website (required)</span>
            </label>
          </div>
          <div className="flex space-x-4">
            <button
              type="submit"
              className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors"
            >
              {showEditForm ? 'Update Artwork' : 'Save Artwork'}
            </button>
            <button
              type="button"
              onClick={() => {
                setShowAddForm(false);
                setShowEditForm(false);
                setSelectedArtwork(null);
                setSelectedFile(null);
                setFormData({
                  title: '',
                  caption: '',
                  display: true,
                  dimension: '',
                  year: ''
                });
              }}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    )}

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {data && data.pages[0].Data.Items.map((artwork: any) => (
        <div key={artwork.ID} className="bg-white rounded-lg shadow-md overflow-hidden">
          <img
            src={artwork.ImageUrl}
            alt={artwork.Title}
            className="w-full h-48 object-cover"
          />
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 mb-2">{artwork.Title}</h3>
            <p className="text-gray-700 text-sm mb-2 line-clamp-2">{artwork.Caption}</p>
            {artwork.Dimension && (
              <p className="text-gray-600 text-sm mb-1">Dimensions: {artwork.Dimension}</p>
            )}
            {artwork.Year && (
              <p className="text-gray-600 text-sm mb-4">Year: {artwork.Year}</p>
            )}
            <div className="flex justify-between items-center">
              <div className="flex space-x-2">
                <button 
                  onClick={() => handleEdit(artwork)}
                  className="text-orange-600 hover:text-orange-700"
                >
                  <Edit size={16} />
                </button>
                <button 
                  onClick={() => handleDelete(artwork.ID)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 size={16} />
                </button>
              </div>
              <div className="text-sm text-gray-500">
                {artwork.Display ? 'Visible' : 'Hidden'}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
  );
}

function VideosTab() {
  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isFetching, isLoading, isError } = useGetAdminBehindTheScene();
  const { mutate: createVideo, isPending: isCreating } = useCreateBehindTheScene();
  const { mutate: updateVideo, isPending: isUpdating } = useUpdateBehindTheScene();
  const { user, setUser } = useAuth();
  const { data: imageSignature } = useGetImageSignature('video');
  const { mutate: uploadVideo, isPending: isUploading } = useUploadCloudinary();

  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    caption: '',
    display: true,
  });

  const handleEdit = (video: any) => {
    setSelectedVideo(video);
    setFormData({
      title: video.Title,
      caption: video.Caption,
      display: video.Display || true,
    });
    setShowEditForm(true);
  };

  const handleDelete = async (videoId: number) => {
    setSelectedVideo({ ID: videoId });
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    try {
      console.log('Deleting video:', selectedVideo.ID);
      setShowDeleteModal(false);
      setSelectedVideo(null);
    } catch (error) {
      console.error('Error deleting video:', error);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (showEditForm) {
        updateVideo({
          data: {
            VideoUrl: selectedVideo.VideoUrl,
            Display: formData.display,
            Caption: formData.caption || undefined,
          },
          id: selectedVideo.ID
        });
      } else {
        if (!selectedFile || !imageSignature?.Data) {
          alert('Please select a video file');
          return;
        }

        uploadVideo(
          {
            file: selectedFile,
            signature: imageSignature.Data.signature,
            timestamp: imageSignature.Data.timestamp,
            api_key: imageSignature.Data.api_key,
            cloud_name: imageSignature.Data.cloud_name,
            resource_type: imageSignature.Data.resource_type,
            upload_preset: imageSignature.Data.upload_preset,
          },
          {
            onSuccess: (videoUrl) => {
              createVideo({
                VideoUrl: videoUrl,
                Display: formData.display,
                Caption: formData.caption || undefined,
              });
            }
          }
        );
      }

      if (!isUploading && !isCreating && !isUpdating) {
        setShowAddForm(false);
        setShowEditForm(false);
        setFormData({
          title: '',
          caption: '',
          display: true,
        });
        setSelectedFile(null);
      }
    } catch (error) {
      console.error('Error saving video:', error);
      alert('Failed to save video. Please try again.');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  if (isLoading || isFetching || isUploading || isCreating || isUpdating) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            {isUploading ? 'Uploading video...' : 
             isCreating ? 'Creating video...' :
             isUpdating ? 'Updating video...' :
             'Loading videos...'}
          </p>
        </div>
      </div>
    );
  }

  if (isError) {
    localStorage.removeItem('auth_user');
    localStorage.removeItem('auth_token');
    setUser(null);
    return null;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Manage Videos</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center"
        >
          <Plus size={20} className="mr-2" />
          Add Video
        </button>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/80 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Delete</h3>
            <p className="text-gray-700 mb-6">Are you sure you want to delete this video? This action cannot be undone.</p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedVideo(null);
                }}
                className="px-4 py-2 border border-gray-200 rounded-lg text-gray-700 hover:text-gray-900 cursor-pointer"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 cursor-pointer"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {(showAddForm || showEditForm) && (
        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">
            {showEditForm ? 'Edit Video' : 'Add New Video'}
          </h3>
          <form onSubmit={handleFormSubmit} className="grid grid-cols-1 gap-4">
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium text-gray-700">
                {showEditForm ? 'Change Video' : 'Upload Video'}
              </label>
              <input
                type="file"
                accept="video/*"
                onChange={handleFileChange}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                required={!showEditForm}
              />
            </div>
            <textarea
              name="caption"
              value={formData.caption}
              onChange={handleInputChange}
              placeholder="Caption"
              rows={3}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  name="display"
                  checked={formData.display}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                />
                <span className="text-sm text-gray-700">Display on website (required)</span>
              </label>
            </div>
            <div className="flex space-x-4">
              <button
                type="submit"
                className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors"
              >
                {showEditForm ? 'Update Video' : 'Save Video'}
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  setShowEditForm(false);
                  setSelectedVideo(null);
                  setSelectedFile(null);
                  setFormData({
                    title: '',
                    caption: '',
                    display: true,
                  });
                }}
                className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {data && data.pages[0].Data.Items.map((video: any) => (
          <div key={video.ID} className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="relative pt-[56.25%]">
              <iframe
                src={video.VideoUrl.replace('watch?v=', 'embed/')}
                title={video.Title}
                className="absolute top-0 left-0 w-full h-full"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
            <div className="p-4">
              <h3 className="font-semibold text-gray-900 mb-2">{video.Title}</h3>
              <p className="text-gray-700 text-sm mb-2 line-clamp-2">{video.Caption}</p>
              <div className="flex justify-between items-center">
                <div className="flex space-x-2">
                  <button 
                    onClick={() => handleEdit(video)}
                    className="text-orange-600 hover:text-orange-700"
                  >
                    <Edit size={16} />
                  </button>
                  <button 
                    onClick={() => handleDelete(video.ID)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
                <div className="text-sm text-gray-500">
                  {video.Display ? 'Visible' : 'Hidden'}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function BioTab() {
  const {data, isLoading, isError} = useGetBio()
  const {mutate: handleAddBio, isPending: isLoadingAddBio, error: createBioError} = useCreateBio()
  const {mutate: handleUpdateBio, isPending: isLoadingUpdateBio, error: updateBioError} = useUpdateBio()

  const [isEditing, setIsEditing] = useState(false);
  const [bioContent, setBioContent] = useState('');

  useEffect(() => {
    if (data) {
      setBioContent(data.Data.Bio || '');
    }
  }, [data]);

  const handleSave = () => {
    if (data.Data.ID === 0) {
      handleAddBio(bioContent);
    } else {
      handleUpdateBio({bio : bioContent, id: data.Data.ID});
    }
    setIsEditing(false);
  };

  if (isLoading || isLoadingAddBio || isLoadingUpdateBio) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading bio content...</p>
        </div>
      </div>
    );
  }

  if (isError || createBioError || updateBioError) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <div className="text-center text-red-600">
          <p className="text-xl font-semibold mb-2">Error</p>
          <p>Failed to load bio content. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Manage Bio</h2>
        <button
          onClick={() => setIsEditing(!isEditing)}
          className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center"
        >
          <Edit size={20} className="mr-2" />
          {isEditing ? 'Cancel' : data.Data.ID === 0 ? 'Add Bio' : 'Edit Bio'}
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        {isEditing ? (
          <div>
            <textarea
              value={bioContent}
              onChange={(e) => setBioContent(e.target.value)}
              rows={15}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="Write your bio here..."
            />
            <div className="flex space-x-4 mt-4">
              <button
                onClick={handleSave}
                className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors"
              >
                Save Changes
              </button>
              <button
                onClick={() => {
                  setIsEditing(false);
                  setBioContent(data.Data.Bio || '');
                }}
                className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div className="prose max-w-none">
            {bioContent.split('\n\n').map((paragraph: string, index: number) => (
              <p key={index} className="text-gray-700 leading-relaxed mb-4 min-h-[74px]">
                {paragraph || 'No bio content available. Click "Add Bio" to add your bio.'}
              </p>
            ))}
            <div className="mt-6 pt-4 border-t border-gray-200">
              {/* <p className="text-sm text-gray-500">
                Last updated: {data?.updatedAt ? new Date(bio.updatedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                }) : 'Never'}
              </p> */}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function ContactTab() {
  const {data, isLoading, isError} = useGetContactInfo()
  const {mutate: handleAddContact, isPending: isLoadingAddContact, error: createContactError} = useCreateContactInfo()
  const {mutate: handleUpdateContact, isPending: isLoadingUpdateContact, error: updateContactError} = useUpdateContactInfo()

  const [isEditing, setIsEditing] = useState(false);
  const [contactInfo, setContactInfo] = useState({
    email: '',
    phone: '',
    whatsapp: '',
    address: '',
    instagram: '',
    twitter: '',
    linkedin: '',
    website: ''
  });

  useEffect(() => {
    if (data && data.Data) {
      setContactInfo({
        email: data.Data.Email || '',
        phone: data.Data.Phone || '',
        whatsapp: data.Data.Whatsapp || '',
        address: data.Data.Address || '',
        instagram: data.Data.Instagram || '',
        twitter: data.Data.Twitter || '',
        linkedin: data.Data.LinkedIn || '',
        website: data.Data.Website || ''
      });
    }
  }, [data]);

  const handleSave = () => {
    if (!data.Data) {
      handleAddContact({
        Email: contactInfo.email,
        Phone: contactInfo.phone,
        Whatsapp: contactInfo.whatsapp,
        Address: contactInfo.address,
        Instagram: contactInfo.instagram,
        Twitter: contactInfo.twitter,
        Linkedin: contactInfo.linkedin,
      });
    } else {
      handleUpdateContact({
        contactInfo: {
          Email: contactInfo.email,
          Phone: contactInfo.phone,
          Whatsapp: contactInfo.whatsapp,
          Address: contactInfo.address,
          Instagram: contactInfo.instagram,
          Twitter: contactInfo.twitter,
          Linkedin: contactInfo.linkedin
        },
        id: data.Data.ID
      });
    }
    setIsEditing(false);
  };

  if (isLoading || isLoadingAddContact || isLoadingUpdateContact) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading contact information...</p>
        </div>
      </div>
    );
  }

  if (isError || createContactError || updateContactError) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <div className="text-center text-red-600">
          <p className="text-xl font-semibold mb-2">Error</p>
          <p>Failed to load contact information. Please try again later.</p>
        </div>
      </div>
    );
  }

  const handleInputChange = (field: string, value: string) => {
    setContactInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Contact Information & Social Media</h2>
        <button
          onClick={() => setIsEditing(!isEditing)}
          className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center"
        >
          <Edit size={20} className="mr-2" />
          {isEditing ? 'Cancel' : !data.Data ? 'Add Contact' : 'Edit Contact'}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Contact Information */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Mail className="mr-2" size={20} />
            Contact Information
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
              {isEditing ? (
                <input
                  type="email"
                  value={contactInfo.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              ) : (
                <p className="text-gray-700 bg-gray-50 px-4 py-2 rounded-lg">{contactInfo.email}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
              {isEditing ? (
                <input
                  type="tel"
                  value={contactInfo.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              ) : (
                <p className="text-gray-700 bg-gray-50 px-4 py-2 rounded-lg">{contactInfo.phone}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">WhatsApp Number</label>
              {isEditing ? (
                <input
                  type="tel"
                  value={contactInfo.whatsapp}
                  onChange={(e) => handleInputChange('whatsapp', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              ) : (
                <p className="text-gray-700 bg-gray-50 px-4 py-2 rounded-lg">{contactInfo.whatsapp}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Address/Location</label>
              {isEditing ? (
                <input
                  type="text"
                  value={contactInfo.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              ) : (
                <p className="text-gray-700 bg-gray-50 px-4 py-2 rounded-lg flex items-center">
                  <MapPin size={16} className="mr-2" />
                  {contactInfo.address}
                </p>
              )}
            </div>

          </div>
        </div>

        {/* Social Media */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Instagram className="mr-2" size={20} />
            Social Media Links
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                <Instagram size={16} className="mr-2" />
                Instagram
              </label>
              {isEditing ? (
                <input
                  type="url"
                  value={contactInfo.instagram}
                  onChange={(e) => handleInputChange('instagram', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="https://instagram.com/username"
                />
              ) : (
                <p className="text-gray-700 bg-gray-50 px-4 py-2 rounded-lg">
                  <a href={contactInfo.instagram} target="_blank" rel="noopener noreferrer" className="text-orange-600 hover:text-orange-700">
                    {contactInfo.instagram}
                  </a>
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                <Twitter size={16} className="mr-2" />
                Twitter
              </label>
              {isEditing ? (
                <input
                  type="url"
                  value={contactInfo.twitter}
                  onChange={(e) => handleInputChange('twitter', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="https://twitter.com/username"
                />
              ) : (
                <p className="text-gray-700 bg-gray-50 px-4 py-2 rounded-lg">
                  <a href={contactInfo.twitter} target="_blank" rel="noopener noreferrer" className="text-orange-600 hover:text-orange-700">
                    {contactInfo.twitter}
                  </a>
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                <Linkedin size={16} className="mr-2" />
                LinkedIn
              </label>
              {isEditing ? (
                <input
                  type="url"
                  value={contactInfo.linkedin}
                  onChange={(e) => handleInputChange('linkedin', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="https://linkedin.com/in/username"
                />
              ) : (
                <p className="text-gray-700 bg-gray-50 px-4 py-2 rounded-lg">
                  <a href={contactInfo.linkedin} target="_blank" rel="noopener noreferrer" className="text-orange-600 hover:text-orange-700">
                    {contactInfo.linkedin}
                  </a>
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Save/Cancel Buttons */}
      {isEditing && (
        <div className="mt-8 flex space-x-4">
          <button
            onClick={handleSave}
            className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 transition-colors"
          >
            Save Changes
          </button>
          <button
            onClick={() => setIsEditing(false)}
            className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
        </div>
      )}
    </div>
  );
}
