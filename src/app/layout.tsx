import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { DataProvider } from "@/contexts/DataContext";
import { AuthProvider } from "@/contexts/AuthContext";
import ReactQueryProviders from "@/lib/react-query/provider";


const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Olaoluwa -  Art Gallery",
  description: "Discover beautiful traditional paintings and behind-the-scenes videos from a passionate artist",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        <ReactQueryProviders>
        <AuthProvider>
          <DataProvider>
            <Navigation />
            <main className="flex-grow -mt-16">
              {children}
            </main>
            <Footer />
          </DataProvider>
        </AuthProvider>
        </ReactQueryProviders>
      </body>
    </html>
  );
}
