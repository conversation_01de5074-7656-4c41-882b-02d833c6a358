'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Play } from 'lucide-react';
import { motion } from 'framer-motion';
import { useData } from '@/contexts/DataContext';
import VideoModal from '@/components/VideoModal';
import { Video } from '@/types';

export default function BehindTheScenesPage() {
  const { videos, isLoading } = useData();
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading videos...</p>
        </div>
      </div>
    );
  }

  const handleVideoClick = (video: Video) => {
    setSelectedVideo(video);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedVideo(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="mt-10 text-5xl font-bold text-gray-900 mb-4">Behind the Scenes</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Get an exclusive look into my creative process. Watch time-lapse videos and learn about the techniques behind each masterpiece.
          </p>
          <div className="w-24 h-1 bg-orange-600 mx-auto mt-6"></div>
        </div>

        {/* Introduction Section */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                The Creative Journey
              </h2>
              <p className="text-lg text-gray-700 leading-relaxed mb-6">
                Every painting tells a story, not just in its final form, but in the journey of its creation. 
                These videos capture the essence of traditional painting techniques, from the first brushstroke 
                to the final details that bring each piece to life.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                Watch as colors blend, textures emerge, and emotions take shape on canvas. Each video offers 
                insights into my artistic process and the passion that drives every creation.
              </p>
            </div>
            <div className="relative">
              <Image
                src="https://images.unsplash.com/photo-1460661419201-fd4cecdf8a8b?w=600&h=400&fit=crop"
                alt="Artist painting process"
                width={600}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>

        {/* Video Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {videos.map((video, index) => (
            <motion.div
              key={video.id}
              className="bg-white rounded-lg shadow-lg overflow-hidden cursor-pointer group"
              onClick={() => handleVideoClick(video)}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5, scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="relative overflow-hidden">
                <Image
                  src={video.thumbnailUrl}
                  alt={video.title}
                  width={400}
                  height={225}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                  <div className="bg-white/90 rounded-full p-4 group-hover:bg-white transition-colors duration-300">
                    <Play size={32} className="text-gray-900 ml-1" />
                  </div>
                </div>
                
                {/* Duration Badge */}
                <div className="absolute bottom-4 right-4">
                  <span className="bg-black/70 text-white px-2 py-1 rounded text-sm font-medium">
                    {video.duration}
                  </span>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {video.title}
                </h3>
                <p className="text-gray-600 mb-3 text-sm">
                  {new Date(video.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
                <p className="text-gray-700 text-sm leading-relaxed line-clamp-3">
                  {video.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Want to See More?
            </h2>
            <p className="text-gray-700 mb-6">
              Follow my artistic journey and be the first to see new behind-the-scenes content.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/gallery"
                className="inline-block bg-orange-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-orange-700 transition-colors"
              >
                View Gallery
              </a>
              <a
                href="/contact"
                className="inline-block bg-gray-900 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-800 transition-colors"
              >
                Get in Touch
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Video Modal */}
      <VideoModal
        video={selectedVideo}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}
