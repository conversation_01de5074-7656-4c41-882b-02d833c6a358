'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, Phone, MapPin, Instagram, Facebook, Twitter, Globe, Copy, Check } from 'lucide-react';
import { mockContactInfo } from '@/data/mockData';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCopy = async (text: string, field: string) => {
    await navigator.clipboard.writeText(text);
    setCopiedField(field);
    setTimeout(() => setCopiedField(null), 2000);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      setSubmitMessage('Thank you for your message! I will get back to you soon.');
      setFormData({ name: '', email: '', subject: '', message: '' });
      setIsSubmitting(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-5xl mt-10 font-bold text-gray-900 mb-4">Contact</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            I'd love to hear from you. Whether you're interested in commissioning a piece, 
            have questions about my work, or just want to say hello.
          </p>
          <div className="w-24 h-1 bg-orange-600 mx-auto mt-6"></div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            className="bg-white rounded-lg shadow-lg p-8"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
              <div className="">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h2>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between group">
                  <div className="flex items-center">
                    <Mail className="text-orange-600 mr-4" size={20} />
                    <div>
                      <p className="font-medium text-gray-900">Email</p>
                      <a href={`mailto:${mockContactInfo.email}`} className="text-gray-600 hover:text-orange-600">
                        {mockContactInfo.email}
                      </a>
                    </div>
                  </div>
                  <button
                    onClick={() => handleCopy(mockContactInfo.email, 'email')}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    {copiedField === 'email' ? (
                      <Check className="text-green-600" size={20} />
                    ) : (
                      <Copy className="text-gray-400 group-hover:text-gray-600" size={20} />
                    )}
                  </button>
                </div>

                {mockContactInfo.phone && (
                  <div className="flex items-center justify-between group">
                    <div className="flex items-center">
                      <Phone className="text-orange-600 mr-4" size={20} />
                      <div>
                        <p className="font-medium text-gray-900">Phone</p>
                        <a href={`tel:${mockContactInfo.phone}`} className="text-gray-600 hover:text-orange-600">
                          {mockContactInfo.phone}
                        </a>
                      </div>
                    </div>
                    <button
                      onClick={() => handleCopy(mockContactInfo.phone!, 'phone')}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      {copiedField === 'phone' ? (
                        <Check className="text-green-600" size={20} />
                      ) : (
                        <Copy className="text-gray-400 group-hover:text-gray-600" size={20} />
                      )}
                    </button>
                  </div>
                )}

                {mockContactInfo.address && (
                  <div className="flex items-center justify-between group">
                    <div className="flex items-center">
                      <MapPin className="text-orange-600 mr-4" size={20} />
                      <div>
                        <p className="font-medium text-gray-900">Address</p>
                        <p className="text-gray-600">{mockContactInfo.address}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => handleCopy(mockContactInfo.address!, 'address')}
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                    >
                      {copiedField === 'address' ? (
                        <Check className="text-green-600" size={20} />
                      ) : (
                        <Copy className="text-gray-400 group-hover:text-gray-600" size={20} />
                      )}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {/* Contact Details */}
         

            {/* Social Media */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Follow My Work</h2>
              
              <div className="grid grid-cols-2 gap-4">
                {mockContactInfo.socialMedia.instagram && (
                  <a
                    href={mockContactInfo.socialMedia.instagram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Instagram className="text-pink-600 mr-3" size={24} />
                    <span className="font-medium text-gray-900">Instagram</span>
                  </a>
                )}

                {mockContactInfo.socialMedia.facebook && (
                  <a
                    href={mockContactInfo.socialMedia.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Facebook className="text-orange-600 mr-3" size={24} />
                    <span className="font-medium text-gray-900">Facebook</span>
                  </a>
                )}

                {mockContactInfo.socialMedia.twitter && (
                  <a
                    href={mockContactInfo.socialMedia.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Twitter className="text-orange-400 mr-3" size={24} />
                    <span className="font-medium text-gray-900">Twitter</span>
                  </a>
                )}

                {mockContactInfo.socialMedia.website && (
                  <a
                    href={mockContactInfo.socialMedia.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Globe className="text-gray-600 mr-3" size={24} />
                    <span className="font-medium text-gray-900">Website</span>
                  </a>
                )}
              </div>
            </div>

            {/* Commission Info */}
            <div className="bg-orange-50 rounded-lg p-8">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Commission Work</h3>
              <p className="text-gray-700 leading-relaxed">
                I accept commission work for custom paintings. Whether you want a portrait, 
                landscape, or something completely unique, I'd love to bring your vision to life. 
                Contact me to discuss your ideas, timeline, and pricing.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
