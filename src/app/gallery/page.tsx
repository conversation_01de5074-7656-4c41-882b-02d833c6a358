'use client';

import { useState } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { useData } from '@/contexts/DataContext';
import ArtworkModal from '@/components/ArtworkModal';
import { Artwork } from '@/types';

export default function GalleryPage() {
  const { artworks, isLoading } = useData();
  const [selectedArtwork, setSelectedArtwork] = useState<Artwork | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading gallery...</p>
        </div>
      </div>
    );
  }

  const handleArtworkClick = (artwork: Artwork) => {
    setSelectedArtwork(artwork);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedArtwork(null);
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent right-click context menu
  };

  const handleDragStart = (e: React.DragEvent) => {
    e.preventDefault(); // Prevent dragging
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-5xl mt-10 font-bold text-gray-900 mb-4">Gallery</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Explore my collection of traditional paintings, each piece telling its own unique story through color, texture, and emotion.
          </p>
          <div className="w-24 h-1 bg-orange-600 mx-auto mt-6"></div>
        </motion.div>

        {/* Gallery Grid - Masonry Style */}
        <div className="columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6">
          {artworks.map((artwork, index) => {
            // Create different heights for masonry effect
            const heights = ['h-64', 'h-80', 'h-96', 'h-72', 'h-88'];
            const randomHeight = heights[index % heights.length];

            return (
              <motion.div
                key={artwork.id}
                className={`relative ${randomHeight} break-inside-avoid mb-6 rounded-lg overflow-hidden cursor-pointer group shadow-lg`}
                onClick={() => handleArtworkClick(artwork)}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Image
                  src={artwork.imageUrl}
                  alt={artwork.title}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-500 select-none"
                  onContextMenu={handleContextMenu}
                  onDragStart={handleDragStart}
                  style={{
                    userSelect: 'none',
                    WebkitUserSelect: 'none',
                    MozUserSelect: 'none',
                    msUserSelect: 'none',
                  }}
                />

                {/* Overlay that appears on hover */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                    <h3 className="text-xl font-bold mb-2 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                      {artwork.title}
                    </h3>
                    <p className="text-sm opacity-90 mb-2 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300 delay-75">
                      {artwork.medium} • {artwork.year}
                    </p>
                    <p className="text-xs opacity-80 line-clamp-2 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300 delay-100">
                      {artwork.description}
                    </p>
                  </div>
                </div>

               
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Artwork Modal */}
      <ArtworkModal
        artwork={selectedArtwork}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}
