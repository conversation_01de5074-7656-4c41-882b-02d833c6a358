'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { Artwork, Video, Bio } from '@/types';
import { DataStorage } from '@/lib/storage';
import { mockArtworks, mockVideos, mockBio } from '@/data/mockData';

interface DataContextType {
  artworks: Artwork[];
  videos: Video[];
  bio: Bio | null;
  addArtwork: (artwork: Artwork) => void;
  updateArtwork: (id: string, artwork: Partial<Artwork>) => void;
  deleteArtwork: (id: string) => void;
  addVideo: (video: Video) => void;
  updateVideo: (id: string, video: Partial<Video>) => void;
  deleteVideo: (id: string) => void;
  updateBio: (bio: Bio) => void;
  isLoading: boolean;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export function DataProvider({ children }: { children: React.ReactNode }) {
  const [artworks, setArtworks] = useState<Artwork[]>([]);
  const [videos, setVideos] = useState<Video[]>([]);
  const [bio, setBio] = useState<Bio | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize data on client side
    const initializeData = () => {
      try {
        // Try to load from localStorage first
        const storedArtworks = DataStorage.getArtworks();
        const storedVideos = DataStorage.getVideos();
        const storedBio = DataStorage.getBio();

        if (storedArtworks.length > 0 || storedVideos.length > 0 || storedBio) {
          setArtworks(storedArtworks);
          setVideos(storedVideos);
          setBio(storedBio);
        } else {
          // Use mock data if no stored data
          setArtworks(mockArtworks);
          setVideos(mockVideos);
          setBio(mockBio);
          
          // Save mock data to localStorage
          DataStorage.saveArtworks(mockArtworks);
          DataStorage.saveVideos(mockVideos);
          DataStorage.saveBio(mockBio);
        }
      } catch (error) {
        console.error('Error loading data:', error);
        // Fallback to mock data
        setArtworks(mockArtworks);
        setVideos(mockVideos);
        setBio(mockBio);
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, []);

  const addArtwork = (artwork: Artwork) => {
    const newArtworks = [...artworks, artwork];
    setArtworks(newArtworks);
    DataStorage.saveArtworks(newArtworks);
  };

  const updateArtwork = (id: string, updatedArtwork: Partial<Artwork>) => {
    const newArtworks = artworks.map(artwork =>
      artwork.id === id ? { ...artwork, ...updatedArtwork } : artwork
    );
    setArtworks(newArtworks);
    DataStorage.saveArtworks(newArtworks);
  };

  const deleteArtwork = (id: string) => {
    const newArtworks = artworks.filter(artwork => artwork.id !== id);
    setArtworks(newArtworks);
    DataStorage.saveArtworks(newArtworks);
  };

  const addVideo = (video: Video) => {
    const newVideos = [...videos, video];
    setVideos(newVideos);
    DataStorage.saveVideos(newVideos);
  };

  const updateVideo = (id: string, updatedVideo: Partial<Video>) => {
    const newVideos = videos.map(video =>
      video.id === id ? { ...video, ...updatedVideo } : video
    );
    setVideos(newVideos);
    DataStorage.saveVideos(newVideos);
  };

  const deleteVideo = (id: string) => {
    const newVideos = videos.filter(video => video.id !== id);
    setVideos(newVideos);
    DataStorage.saveVideos(newVideos);
  };

  const updateBio = (newBio: Bio) => {
    setBio(newBio);
    DataStorage.saveBio(newBio);
  };

  const value: DataContextType = {
    artworks,
    videos,
    bio,
    addArtwork,
    updateArtwork,
    deleteArtwork,
    addVideo,
    updateVideo,
    deleteVideo,
    updateBio,
    isLoading,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
}

export function useData() {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
}
