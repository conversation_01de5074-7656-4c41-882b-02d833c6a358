'use client';

import { $http, addAccessTokenToHttpInstance } from '@/lib/http';
import axios from 'axios';
import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  username: string;
  email: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  setUser: (user: User | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);


export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in (from localStorage)
    const checkAuthStatus = () => {
      try {
        const storedUser = localStorage.getItem('auth_user');
        const authToken = localStorage.getItem('auth_token');
        
        if (storedUser && authToken) {
          setUser(JSON.parse(storedUser));
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        // Clear invalid data
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_token');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
     
     const reponse =  await $http.post('hidemyasspls/login', { email: username, password });
     if (reponse.status === 200) {
       const authToken = reponse.data.Data.Token;
       
       // Store user and token
       localStorage.setItem('auth_user', JSON.stringify(reponse.data.Data.Email));
       localStorage.setItem('auth_token', authToken);
       
       
       setUser({
         username: reponse.data.Data.Username,
         email: reponse.data.Data.Email
       });
       addAccessTokenToHttpInstance(authToken);
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const logout = () => {
    // Clear stored data
    localStorage.removeItem('auth_user');
    localStorage.removeItem('auth_token');
    
    setUser(null);
  };


  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    setUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
