import { Artwork, Video, Bio } from '@/types';

// In a real application, this would connect to a database
// For now, we'll use localStorage for persistence

export class DataStorage {
  private static ARTWORKS_KEY = 'portfolio_artworks';
  private static VIDEOS_KEY = 'portfolio_videos';
  private static BIO_KEY = 'portfolio_bio';

  // Artworks
  static getArtworks(): Artwork[] {
    if (typeof window === 'undefined') return [];
    const stored = localStorage.getItem(this.ARTWORKS_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  static saveArtworks(artworks: Artwork[]): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.ARTWORKS_KEY, JSON.stringify(artworks));
  }

  static addArtwork(artwork: Artwork): void {
    const artworks = this.getArtworks();
    artworks.push(artwork);
    this.saveArtworks(artworks);
  }

  static updateArtwork(id: string, updatedArtwork: Partial<Artwork>): void {
    const artworks = this.getArtworks();
    const index = artworks.findIndex(a => a.id === id);
    if (index !== -1) {
      artworks[index] = { ...artworks[index], ...updatedArtwork };
      this.saveArtworks(artworks);
    }
  }

  static deleteArtwork(id: string): void {
    const artworks = this.getArtworks();
    const filtered = artworks.filter(a => a.id !== id);
    this.saveArtworks(filtered);
  }

  // Videos
  static getVideos(): Video[] {
    if (typeof window === 'undefined') return [];
    const stored = localStorage.getItem(this.VIDEOS_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  static saveVideos(videos: Video[]): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.VIDEOS_KEY, JSON.stringify(videos));
  }

  static addVideo(video: Video): void {
    const videos = this.getVideos();
    videos.push(video);
    this.saveVideos(videos);
  }

  static updateVideo(id: string, updatedVideo: Partial<Video>): void {
    const videos = this.getVideos();
    const index = videos.findIndex(v => v.id === id);
    if (index !== -1) {
      videos[index] = { ...videos[index], ...updatedVideo };
      this.saveVideos(videos);
    }
  }

  static deleteVideo(id: string): void {
    const videos = this.getVideos();
    const filtered = videos.filter(v => v.id !== id);
    this.saveVideos(filtered);
  }

  // Bio
  static getBio(): Bio | null {
    if (typeof window === 'undefined') return null;
    const stored = localStorage.getItem(this.BIO_KEY);
    return stored ? JSON.parse(stored) : null;
  }

  static saveBio(bio: Bio): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.BIO_KEY, JSON.stringify(bio));
  }

  // Utility functions
  static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  static initializeWithMockData(): void {
    // Only initialize if no data exists
    if (this.getArtworks().length === 0) {
      // Import mock data dynamically to avoid SSR issues
      import('@/data/mockData').then(({ mockArtworks, mockVideos, mockBio }) => {
        this.saveArtworks(mockArtworks);
        this.saveVideos(mockVideos);
        this.saveBio(mockBio);
      });
    }
  }
}

// File upload utilities
export class FileUpload {
  static async uploadImage(file: File): Promise<string> {
    // In a real application, this would upload to a cloud storage service
    // For now, we'll create a local URL
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    });
  }

  static async uploadVideo(file: File): Promise<string> {
    // In a real application, this would upload to a video hosting service
    // For now, we'll create a local URL
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    });
  }

  static validateImageFile(file: File): boolean {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB
    
    return allowedTypes.includes(file.type) && file.size <= maxSize;
  }

  static validateVideoFile(file: File): boolean {
    const allowedTypes = ['video/mp4', 'video/webm', 'video/ogg'];
    const maxSize = 100 * 1024 * 1024; // 100MB
    
    return allowedTypes.includes(file.type) && file.size <= maxSize;
  }
}
