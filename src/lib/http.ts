'use client';

import axios from 'axios';
import { API_URL, APP_URL } from './constants';


export const $http = axios.create({
  baseURL: API_URL,
  headers: {
    'Access-Control-Allow-Origin': APP_URL,
  },
});

$http.interceptors.response.use(
  response => response,
  error => {
    return Promise.reject(error);
  },
);

export const addAccessTokenToHttpInstance = (token: string) => {
  $http.interceptors.request.use(
    config => {
      config.headers['Authorization'] = `Bearer ${token}`;
      return config;
    },
    error => {
      return Promise.reject(error);
    },
  );
};
