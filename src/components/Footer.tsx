import Link from 'next/link';
import { Instagram, Facebook, Twitter, Globe } from 'lucide-react';
import { mockContactInfo } from '@/data/mockData';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-50 text-white pb-5">
      <div className="max-w-7xl px-4 sm:px-6 lg:px-8 mx-auto ">
        <div className="backdrop-blur-md bg-gradient-to-tr from-black/10 via-white to-white rounded-2xl px-8 py-4 border border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
            {/* About Section */}
            <div>
              <h3 className="text-xl font-bold text-black">Artist Portfolio</h3>
            </div>

            
            <div>
            
              <div className="space-x-2 flex items-center">
                {mockContactInfo.socialMedia.instagram && (
                  <a
                    href={mockContactInfo.socialMedia.instagram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-gray-600 hover:font-black transition-all duration-300 ease-in-out transform hover:scale-105 hover:text-pink-600"
                  >
                    <span>Instagram</span>
                  </a>
                )}
                {mockContactInfo.socialMedia.facebook && (
                  <a
                    href={mockContactInfo.socialMedia.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-gray-600 hover:font-black transition-all duration-300 ease-in-out transform hover:scale-105 hover:text-blue-600"
                  >
                    <span>Facebook</span>
                  </a>
                )}
                {mockContactInfo.socialMedia.twitter && (
                  <a
                    href={mockContactInfo.socialMedia.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-gray-600 hover:font-black transition-all duration-300 ease-in-out transform hover:scale-105 hover:text-blue-400"
                  >
                    <span>Twitter</span>
                  </a>
                )}
                
              </div>
            </div>

            {/* Contact Info */}
            <div className='flex justify-end '>
              <div className='flex justify-center items-center gap-2 bg-gray-100 border border-gray-200 p-2 rounded-full'>
                <div className='w-2 h-2 rounded-full bg-green-600'></div>
                <p className="text-xs font-bold text-black uppercase">Available for Job</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
