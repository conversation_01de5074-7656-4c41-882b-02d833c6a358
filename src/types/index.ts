export interface Artwork {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  year: number;
  medium: string;
  dimensions: string;
  price?: number;
  isAvailable: boolean;
  createdAt: string;
}

export interface Video {
  id: string;
  title: string;
  description: string;
  videoUrl: string;
  thumbnailUrl: string;
  duration: string;
  createdAt: string;
}

export interface Bio {
  id: string;
  content: string;
  updatedAt: string;
}

export interface ContactInfo {
  email: string;
  phone?: string;
  address?: string;
  socialMedia: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    website?: string;
  };
}
