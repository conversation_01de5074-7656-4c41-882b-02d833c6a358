import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useCreateBio = () => {
  const queryClient = useQueryClient();
  const { isAuthenticated , setUser } = useAuth();
  const token = localStorage.getItem('auth_token');

  return useMutation({
    mutationFn: async (bio: string) => {
      if (!isAuthenticated) throw new Error("Not authenticated");
      
      const response = await $http.post('hidemyasspls/create-bio', {
        Bio: bio
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });


      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['bio'] });
    },
    onError: (error) => {
      console.error('Error creating bio:', error);
      if ((error as any)?.response?.status === 401) {
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_token');
        setUser(null);
      }
    },
  });
};
