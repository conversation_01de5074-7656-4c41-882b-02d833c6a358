import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useCreateContactInfo = () => {
  const queryClient = useQueryClient();
  const { isAuthenticated, setUser } = useAuth();
  const token = localStorage.getItem('auth_token');

  return useMutation({
    mutationFn: async (contactInfo: {
      Email: string;
      Whatsapp: string;
      Address: string;
    Instagram: string;
    Twitter: string;
    Linkedin: string;
    Phone: string;
    
    }) => {
      if (!isAuthenticated) throw new Error("Not authenticated");
      
      const response = await $http.post('/hidemyasspls/create-contact', contactInfo, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contact-info'] });
    },
    onError: (error) => {
      console.error('Error creating contact info:', error);
      if ((error as any)?.response?.status === 401) {
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_token');
        setUser(null);
      }
    },
  });
};
