import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { useMutation } from "@tanstack/react-query";

export const useUploadCloudinary = () => {
  const { isAuthenticated } = useAuth();
  const token = localStorage.getItem('auth_token');

  return useMutation({
    mutationFn: async ({ file, signature, timestamp, api_key, cloud_name, resource_type, upload_preset }: {
      file: File;
      signature: string;
      timestamp: number;
      api_key: string;
      cloud_name: string;
      resource_type: string;
      upload_preset: string;
    }) => {
      if (!isAuthenticated) throw new Error("Not authenticated");

      const formData = new FormData();
      formData.append('file', new Blob([file], { type: file.type }));
      formData.append('signature', signature);
      formData.append('timestamp', timestamp.toString());
      formData.append('api_key', api_key);
      formData.append('cloud_name', cloud_name);
      formData.append('resource_type', resource_type);
      formData.append('upload_preset', upload_preset);

      const response = await fetch(`https://api.cloudinary.com/v1_1/${cloud_name}/${resource_type}/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const data = await response.json();
      console.log('data resjdk', data.secure_url);
      return data.secure_url;
    },
    onError: (error) => {
      console.error('Error uploading to Cloudinary:', error);
    },
  });
};
