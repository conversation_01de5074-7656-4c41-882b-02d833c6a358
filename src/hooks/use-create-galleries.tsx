import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useCreateGallery = () => {
  const queryClient = useQueryClient();
  const { isAuthenticated, setUser } = useAuth();
  const token = localStorage.getItem('auth_token');

  return useMutation({
    mutationFn: async (data: {
      ImageUrl: string;
      Display: boolean;
      Title?: string;
      Caption?: string;
      Dimension?: string;
      Year?: string
    }) => {
      if (!isAuthenticated) throw new Error("Not authenticated");
      
      const response = await $http.post('hidemyasspls/create-gallery', data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-galleries'] });
    },
    onError: (error) => {
      console.error('Error creating gallery:', error);
      if ((error as any)?.response?.status === 401) {
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_token');
        setUser(null);
      }
    },
  });
};
