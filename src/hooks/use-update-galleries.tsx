import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useUpdateGallery = () => {
  const queryClient = useQueryClient();
  const { isAuthenticated, setUser } = useAuth();
  const token = localStorage.getItem('auth_token');

  return useMutation({
    mutationFn: async ({data, id}: {
      data: {
        ImageUrl: string;
        Display: boolean;
        Title?: string;
        Caption?: string;
        Dimension?: string;
        Year?: string
      }, 
      id: string
    }) => {
      if (!isAuthenticated) throw new Error("Not authenticated");
      
      const response = await $http.patch(`hidemyasspls/update-single-painting/${id}`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-galleries'] });
    },
    onError: (error) => {
      console.error('Error updating gallery:', error);
      if ((error as any)?.response?.status === 401) {
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_token');
        setUser(null);
      }
    },
  });
};
