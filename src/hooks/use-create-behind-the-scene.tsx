import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useCreateBehindTheScene = () => {
  const queryClient = useQueryClient();
  const { isAuthenticated, setUser } = useAuth();
  const token = localStorage.getItem('auth_token');

  return useMutation({
    mutationFn: async (data: {
      VideoUrl: string;
      Caption?: string;
      Display: boolean;
    }) => {
      if (!isAuthenticated) throw new Error("Not authenticated");
      
      const response = await $http.post('hidemyasspls/create-behind-the-scenes', data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-behind-the-scenes'] });
    },
    onError: (error) => {
      console.error('Error creating behind the scene:', error);
      if ((error as any)?.response?.status === 401) {
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_token');
        setUser(null);
      }
    },
  });
};
