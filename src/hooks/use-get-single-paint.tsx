import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

export const useGetSinglePaint = (id: string) => {
    const { isAuthenticated } = useAuth();
    const queryKey = ['single-paint', id];
    
    return useQuery({
      queryKey,
      queryFn: async () => {
        if (!isAuthenticated) return;
        const response = await $http.get(`/get-single-painting/${id}`);
  
        return response.data;
      },
      placeholderData: keepPreviousData,
      enabled: !!isAuthenticated && !!id,
    });
};
