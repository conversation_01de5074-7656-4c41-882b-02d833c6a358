import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

export const useGetContactInfo = () => {
    const { isAuthenticated } = useAuth();
    const queryKey = ['contact-info'];
    
    return useQuery({
      queryKey,
      queryFn: async () => {
        if (!isAuthenticated) return;
        const response = await $http.get('/get-contact');
  
        return response.data;
      },
      placeholderData: keepPreviousData,
      enabled: !!isAuthenticated,
    });
};
