import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

export const useGetBio = () => {
    const { isAuthenticated } = useAuth();
    const queryKey = ['bio'];
    return useQuery({
      queryKey,
      queryFn: async () => {
        if (!isAuthenticated) return;
        const response = await $http.get('/get-bio');
  
        return response.data;
      },
      placeholderData: keepPreviousData,
      enabled: !!isAuthenticated,
    });
  };
  