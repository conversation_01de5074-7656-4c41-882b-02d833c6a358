import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useUpdateBehindTheScene = () => {
  const queryClient = useQueryClient();
  const { isAuthenticated, setUser } = useAuth();
  const token = localStorage.getItem('auth_token');

  return useMutation({
    mutationFn: async ({data, id}: {
      data: {
        VideoUrl: string;
        Caption?: string;
        Display: boolean;
      }, 
      id: string
    }) => {
      if (!isAuthenticated) throw new Error("Not authenticated");
      
      const response = await $http.patch(`hidemyasspls/update-behind-the-scenes/${id}`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-behind-the-scenes'] });
    },
    onError: (error) => {
      console.error('Error updating behind the scene:', error);
      if ((error as any)?.response?.status === 401) {
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_token');
        setUser(null);
      }
    },
  });
};
