import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { useInfiniteQuery } from "@tanstack/react-query";

export const useGetAdminGalleries = () => {
    const { isAuthenticated } = useAuth();
    const queryKey = ['admin-galleries'];
    const token = localStorage.getItem('auth_token');
    
    return useInfiniteQuery({
      queryKey,
      queryFn: async ({ pageParam = 0 }) => {
        if (!isAuthenticated) return;
        const response = await $http.get(`hidemyasspls/get-galleries?PerPage=10&Page=${pageParam}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        return response.data;
      },
      getNextPageParam: (lastPage, pages) => {
        if (!lastPage || lastPage.length < 6) return undefined;
        return pages.length;
      },
      initialPageParam: 0,
      enabled: !!isAuthenticated,
    });
};
