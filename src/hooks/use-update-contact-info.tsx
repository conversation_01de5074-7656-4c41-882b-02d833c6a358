import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useUpdateContactInfo = () => {
  const queryClient = useQueryClient();
  const { isAuthenticated, setUser } = useAuth();
  const token = localStorage.getItem('auth_token');

  return useMutation({
    mutationFn: async ({contactInfo, id}: {
      contactInfo: {
        Email: string;
        Whatsapp: string;
        Address: string;
        Instagram: string;
        Twitter: string;
        Linkedin: string;
        Phone: string;
        
      }, 
      id: string
    }) => {
      if (!isAuthenticated) throw new Error("Not authenticated");
      
      const response = await $http.patch(`/hidemyasspls/update-contact/${id}`, contactInfo, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contact-info'] });
    },
    onError: (error) => {
      console.error('Error updating contact info:', error);
      if ((error as any)?.response?.status === 401) {
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_token');
        setUser(null);
      }
    },
  });
};
