import { useAuth } from "@/contexts/AuthContext";
import { $http } from "@/lib/http";
import { useInfiniteQuery } from "@tanstack/react-query";

export const useGetBehindTheScene = () => {
    const { isAuthenticated } = useAuth();
    const queryKey = ['behind-the-scenes'];
    
    return useInfiniteQuery({
      queryKey,
      queryFn: async ({ pageParam = 0 }) => {
        if (!isAuthenticated) return;
        const response = await $http.get(`/get-behind-the-scenes?PerPage=2&Page=${pageParam}`);
        return response.data;
      },
      getNextPageParam: (lastPage, pages) => {
        if (!lastPage || lastPage.length < 2) return undefined;
        return pages.length;
      },
      initialPageParam: 0,
      enabled: !!isAuthenticated,
    });
};
